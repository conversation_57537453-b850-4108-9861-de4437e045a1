# Rust specific
/target/
**/target/
debug/
release/
Cargo.lock
**/*.rs.bk

# Build artifacts
*.rlib
*.rmeta
*.d
*.o
*.so
*.dylib
*.dll
*.a
*.pdb

# Model files (should be handled by Git LFS or submodules)
*.gguf
*.bin
models/
external/*
!external/llama.cpp/

# Environment files
.env
.env.local
.env.*
config.local.*

# Editor and OS files (shared across projects)
.idea/
.vscode/
*.swp
*.swo
.DS_Store
**/.DS_Store

# Log files
/logs/
*.log

# Temporary files
*.tmp
*.temp
.history
/temp/

# Documentation
doc/api/

# MSVC Windows builds of rustc generate these, which store debugging information
*.pdb

# macOS specific
.DS_Store
**/.DS_Store

# IDE specific
.idea/
.vscode/
*.swp
*.swo

# Environment files
.env
.env.local

# Flutter/Dart specific
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
build/
ios/Pods/
ios/.symlinks/
ios/Flutter/Flutter.framework/
ios/Flutter/Flutter.podspec
ios/Flutter/Generated.xcconfig
ios/Flutter/App.framework/
ios/Flutter/flutter_export_environment.sh
macos/Flutter/
macos/Pods/
macos/.symlinks/
.metadata

# Generated files
*.g.dart
*.freezed.dart
*.mocks.dart
*.config.dart

# Test coverage
coverage/
.test_coverage.dart
lcov.info

# Media
/media
media/